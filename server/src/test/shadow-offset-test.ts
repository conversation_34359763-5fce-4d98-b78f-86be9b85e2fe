import { TextElementASSUtils } from "../ffmpeg/utils/textElementASSUtils";
import { CaptionStyle, Caption } from "../ffmpeg/types";

/**
 * 测试阴影偏移的坐标系统
 */
function testShadowOffset() {
  console.log("=== 阴影偏移测试 ===");

  // 测试用例：不同的阴影偏移值
  const testCases = [
    { shadowOffsetX: 10, shadowOffsetY: 5, description: "正值偏移 (右下)" },
    { shadowOffsetX: -10, shadowOffsetY: -5, description: "负值偏移 (左上)" },
    { shadowOffsetX: 15, shadowOffsetY: 0, description: "仅X轴偏移 (右)" },
    { shadowOffsetX: 0, shadowOffsetY: 10, description: "仅Y轴偏移 (下)" },
    { shadowOffsetX: -8, shadowOffsetY: 12, description: "混合偏移 (左下)" },
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n--- 测试 ${index + 1}: ${testCase.description} ---`);

    const style: CaptionStyle = {
      fontSize: 24,
      fontFamily: "Arial",
      fontColor: "#ffffff",
      fontWeight: 400,
      textAlign: "center",
      lineHeight: 1.2,
      charSpacing: 0,
      styles: [],
      strokeWidth: 0,
      strokeColor: "#000000",
      shadowColor: "#000000",
      shadowBlur: 3,
      shadowOffsetX: testCase.shadowOffsetX,
      shadowOffsetY: testCase.shadowOffsetY,
      backgroundColor: "transparent",
      useGradient: false,
      gradientColors: ["#ffffff", "#000000"],
      positionX: 100,
      positionY: 100,
      width: 200,
      height: 50,
      opacity: 1,
      originX: "center",
      originY: "top",
    };

    // 创建测试字幕
    const caption: Caption = {
      id: `test-caption-${index}`,
      text: "测试阴影偏移",
      startTime: "00:00:00.000",
      endTime: "00:00:05.000",
    };

    try {
      // 生成ASS文件
      const assFilePath = TextElementASSUtils.createTextElementASSFile(
        caption,
        style,
        1920,
        1080,
        1.0
      );

      console.log(`ASS文件已生成: ${assFilePath}`);
      console.log(
        `前端设置: shadowOffsetX=${testCase.shadowOffsetX}, shadowOffsetY=${testCase.shadowOffsetY}`
      );

      // 读取生成的ASS文件内容
      const fs = require("fs");
      const assContent = fs.readFileSync(assFilePath, "utf8");

      console.log("=== ASS文件内容 ===");
      console.log(assContent);
      console.log("=== ASS文件内容结束 ===");

      // 提取阴影相关标签
      const xshadMatch = assContent.match(/\\xshad(-?\d+)/);
      const yshadMatch = assContent.match(/\\yshad(-?\d+)/);
      const shadMatch = assContent.match(/\\shad(\d+)/);

      if (xshadMatch || yshadMatch) {
        const assXshad = xshadMatch ? parseInt(xshadMatch[1]) : 0;
        const assYshad = yshadMatch ? parseInt(yshadMatch[1]) : 0;
        const assShad = shadMatch ? parseInt(shadMatch[1]) : 0;

        console.log(
          `ASS输出: \\xshad${assXshad}, \\yshad${assYshad}, \\shad${assShad}`
        );

        // 检查是否一致
        const xConsistent = assXshad === testCase.shadowOffsetX;
        const yConsistent = assYshad === testCase.shadowOffsetY;

        console.log(
          `X轴一致性: ${xConsistent ? "✅" : "❌"} (前端: ${
            testCase.shadowOffsetX
          }, ASS: ${assXshad})`
        );
        console.log(
          `Y轴一致性: ${yConsistent ? "✅" : "❌"} (前端: ${
            testCase.shadowOffsetY
          }, ASS: ${assYshad})`
        );

        if (!xConsistent || !yConsistent) {
          console.log("⚠️  发现阴影偏移不一致！");
        }
      } else {
        console.log("❌ 未找到阴影偏移标签");
      }

      // 清理测试文件
      fs.unlinkSync(assFilePath);
    } catch (error) {
      console.error(
        `测试失败: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  });

  console.log("\n=== 测试完成 ===");
}

// 运行测试
if (require.main === module) {
  testShadowOffset();
}

export { testShadowOffset };
